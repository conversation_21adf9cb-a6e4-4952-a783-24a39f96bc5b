/**
 * React 19 Compatibility Utilities
 * 
 * This module provides utilities to handle React 19 compatibility issues,
 * particularly with ref composition and Radix UI components.
 */

import * as React from "react";

/**
 * React 19 compatible ref composition utility
 * 
 * This replaces @radix-ui/react-compose-refs to prevent infinite loops
 * in React 19's stricter ref handling.
 */
export function composeRefs<T = any>(...refs: Array<React.Ref<T> | undefined>): React.RefCallback<T> {
  return (node: T) => {
    refs.forEach((ref) => {
      if (typeof ref === "function") {
        ref(node);
      } else if (ref != null) {
        (ref as React.MutableRefObject<T | null>).current = node;
      }
    });
  };
}

/**
 * React 19 compatible useComposedRefs hook
 */
export function useComposedRefs<T = any>(...refs: Array<React.Ref<T> | undefined>): React.RefCallback<T> {
  return React.useCallback(composeRefs(...refs), refs);
}

/**
 * Test environment detection
 */
export const isTestEnvironment = typeof process !== "undefined" && process.env.NODE_ENV === "test";

/**
 * React 19 compatible forwardRef wrapper that handles test environment issues
 */
export function createForwardRefComponent<T, P = {}>(
  displayName: string,
  render: (props: P, ref: React.Ref<T>) => React.ReactElement | null
) {
  const Component = React.forwardRef<T, P>(render);
  Component.displayName = displayName;
  
  // In test environment, wrap with additional error boundary handling
  if (isTestEnvironment) {
    const TestWrapper = React.forwardRef<T, P>((props, ref) => {
      try {
        return React.createElement(Component, { ...props, ref });
      } catch (error) {
        console.warn(`Error in ${displayName}:`, error);
        return null;
      }
    });
    TestWrapper.displayName = `${displayName}TestWrapper`;
    return TestWrapper;
  }
  
  return Component;
}

/**
 * Safe ref setter that prevents React 19 infinite loops
 */
export function setRef<T>(ref: React.Ref<T> | undefined, value: T | null): void {
  if (typeof ref === "function") {
    ref(value);
  } else if (ref && typeof ref === "object" && "current" in ref) {
    (ref as React.MutableRefObject<T | null>).current = value;
  }
}

/**
 * React 19 compatible useCallback with ref dependencies
 */
export function useRefCallback<T extends (...args: any[]) => any>(
  callback: T,
  refs: Array<React.Ref<any> | undefined>
): T {
  return React.useCallback(callback, [callback, ...refs]) as T;
}
