/**
 * Core type definitions for the Disc Golf Inventory Management System
 *
 * This file contains all TypeScript interfaces, enums, and types used throughout
 * the application for disc golf inventory management.
 */

// ============================================================================
// ENUMS
// ============================================================================

/**
 * Represents the condition/wear state of a disc
 */
export enum DiscCondition {
  NEW = "new",
  GOOD = "good",
  FAIR = "fair",
  WORN = "worn",
  DAMAGED = "damaged",
}

/**
 * Represents the current storage location of a disc
 */
export enum Location {
  BAG = "bag",
  CAR = "car",
  HOME = "home",
  LOANED = "loaned",
  LOST = "lost",
}

// ============================================================================
// CORE INTERFACES
// ============================================================================

/**
 * Flight characteristics of a disc golf disc
 * These numbers describe how the disc flies through the air
 */
export interface FlightNumbers {
  /** Speed: How fast the disc needs to be thrown (1-14) */
  speed: number;
  /** Glide: How long the disc stays in the air (1-7) */
  glide: number;
  /** Turn: High speed stability (-5 to +1) */
  turn: number;
  /** Fade: Low speed stability (0-5) */
  fade: number;
}

/**
 * Complete disc entity representing a disc golf disc in the inventory
 */
export interface Disc {
  /** Unique identifier (UUID v4) */
  id: string;
  /** Disc manufacturer (e.g., "Innova", "Discraft", "Dynamic Discs") */
  manufacturer: string;
  /** Disc mold/model (e.g., "Destroyer", "Buzzz", "Judge") */
  mold: string;
  /** Plastic type (e.g., "Champion", "ESP", "Lucid") */
  plasticType: string;
  /** Disc weight in grams (150-180) */
  weight: number;
  /** Current condition of the disc */
  condition: DiscCondition;
  /** Flight characteristics */
  flightNumbers: FlightNumbers;
  /** Primary disc color */
  color: string;
  /** Optional user notes (max 500 characters) */
  notes?: string;
  /** Date when disc was acquired */
  purchaseDate?: Date;
  /** Price paid in USD */
  purchasePrice?: number;
  /** Current storage location */
  currentLocation: Location;
  /** Optional disc image URL */
  imageUrl?: string;
  /** Record creation timestamp */
  createdAt: Date;
  /** Last modification timestamp */
  updatedAt: Date;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Type for creating a new disc (without auto-generated fields)
 */
export type CreateDiscInput = Omit<Disc, "id" | "createdAt" | "updatedAt">;

/**
 * Type for updating an existing disc (all fields optional except id)
 */
export type UpdateDiscInput = Partial<Omit<Disc, "id" | "createdAt">> & {
  id: string;
  updatedAt: Date;
};

/**
 * Type for disc search/filter criteria
 */
export interface DiscFilterCriteria {
  manufacturer?: string;
  mold?: string;
  plasticType?: string;
  condition?: DiscCondition;
  location?: Location;
  minWeight?: number;
  maxWeight?: number;
  minSpeed?: number;
  maxSpeed?: number;
  color?: string;
  searchTerm?: string;
}

/**
 * Type for sorting options
 */
export interface DiscSortOptions {
  field: keyof Disc;
  direction: "asc" | "desc";
}

// ============================================================================
// VALIDATION TYPES
// ============================================================================

/**
 * Validation result type
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

/**
 * Individual validation error
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// ============================================================================
// UI COMPONENT TYPES
// ============================================================================

/**
 * Props for disc-related components
 */
export interface DiscCardProps {
  disc: Disc;
  onEdit?: (disc: Disc) => void;
  onDelete?: (discId: string) => void;
  onSelect?: (disc: Disc) => void;
  isSelected?: boolean;
  showActions?: boolean;
}

/**
 * Props for flight numbers display component
 */
export interface FlightNumbersDisplayProps {
  flightNumbers: FlightNumbers;
  size?: "sm" | "md" | "lg";
  showLabels?: boolean;
  showTooltips?: boolean;
}

/**
 * Props for disc condition badge component
 */
export interface DiscConditionBadgeProps {
  condition: DiscCondition;
  size?: "sm" | "md" | "lg";
}

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Flight number validation ranges
 */
export const FLIGHT_NUMBER_RANGES = {
  speed: { min: 1, max: 14 },
  glide: { min: 1, max: 7 },
  turn: { min: -5, max: 1 },
  fade: { min: 0, max: 5 },
} as const;

/**
 * Disc weight validation range
 */
export const DISC_WEIGHT_RANGE = {
  min: 150,
  max: 180,
} as const;

/**
 * Maximum character limits
 */
export const CHARACTER_LIMITS = {
  manufacturer: 50,
  mold: 50,
  plasticType: 30,
  color: 30,
  notes: 500,
} as const;
