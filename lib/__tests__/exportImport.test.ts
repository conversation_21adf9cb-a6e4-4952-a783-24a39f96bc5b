/**
 * Export/Import Functionality Tests
 *
 * Tests for the export/import functionality including JSON/CSV export,
 * JSON import with validation, and error handling.
 */

import { describe, it, expect, beforeEach } from "vitest";
import type { Disc } from "../types";
import { DiscCondition, Location } from "../types";
import { exportCollection, generateExportFilename, importFromJson, type ExportOptions } from "../exportImport";

// Test data
const mockDisc: Disc = {
  id: "123e4567-e89b-12d3-a456-426614174000",
  manufacturer: "Innova",
  mold: "Destroyer",
  plasticType: "Champion",
  weight: 175,
  condition: DiscCondition.NEW,
  flightNumbers: {
    speed: 12,
    glide: 5,
    turn: -1,
    fade: 3,
  },
  color: "Blue",
  notes: "Test disc",
  purchaseDate: new Date("2024-01-01"),
  purchasePrice: 20.99,
  currentLocation: Location.BAG,
  imageUrl: "https://example.com/disc.jpg",
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
};

// Expected disc after JSON serialization/deserialization
const expectedDiscAfterSerialization = {
  ...mockDisc,
  purchaseDate: "2024-01-01T00:00:00.000Z",
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z",
};

const mockDiscs: Disc[] = [mockDisc];

describe("Export Functionality", () => {
  describe("exportCollection", () => {
    it("should export discs as JSON with metadata", () => {
      const options: ExportOptions = {
        format: "json",
        includeMetadata: true,
        prettyFormat: true,
      };

      const result = exportCollection(mockDiscs, options);
      const parsed = JSON.parse(result);

      expect(parsed).toHaveProperty("metadata");
      expect(parsed).toHaveProperty("discs");
      expect(parsed.metadata).toHaveProperty("exportDate");
      expect(parsed.metadata).toHaveProperty("totalDiscs", 1);
      expect(parsed.metadata).toHaveProperty("version", "1.0");
      expect(parsed.discs).toHaveLength(1);
      expect(parsed.discs[0]).toEqual(expectedDiscAfterSerialization);
    });

    it("should export discs as JSON without metadata", () => {
      const options: ExportOptions = {
        format: "json",
        includeMetadata: false,
        prettyFormat: true,
      };

      const result = exportCollection(mockDiscs, options);
      const parsed = JSON.parse(result);

      expect(Array.isArray(parsed)).toBe(true);
      expect(parsed).toHaveLength(1);
      expect(parsed[0]).toEqual(expectedDiscAfterSerialization);
    });

    it("should export discs as CSV", () => {
      const options: ExportOptions = {
        format: "csv",
      };

      const result = exportCollection(mockDiscs, options);

      expect(result).toContain('"ID","Manufacturer","Mold"');
      expect(result).toContain("Innova");
      expect(result).toContain("Destroyer");
      expect(result).toContain("Champion");
    });

    it("should handle empty disc array", () => {
      const options: ExportOptions = {
        format: "json",
      };

      const result = exportCollection([], options);
      const parsed = JSON.parse(result);

      expect(parsed.metadata.totalDiscs).toBe(0);
      expect(parsed.discs).toHaveLength(0);
    });

    it("should throw error for unsupported format", () => {
      const options = {
        format: "xml" as any,
      };

      expect(() => exportCollection(mockDiscs, options)).toThrow("Unsupported export format");
    });
  });

  describe("generateExportFilename", () => {
    it("should generate filename with current date", () => {
      const filename = generateExportFilename("json");
      const today = new Date().toISOString().split("T")[0];

      expect(filename).toContain(today);
      expect(filename).toContain(".json");
      expect(filename).toContain("disc-golf-inventory");
    });

    it("should use custom name prefix", () => {
      const filename = generateExportFilename("csv", "my-collection");

      expect(filename).toContain("my-collection");
      expect(filename).toContain(".csv");
    });
  });
});

describe("Import Functionality", () => {
  describe("importFromJson", () => {
    it("should import valid JSON data", () => {
      const jsonData = JSON.stringify(mockDiscs);
      const result = importFromJson(jsonData, []);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0]).toEqual(mockDisc);
      expect(result.summary).toBeDefined();
      expect(result.summary!.totalImported).toBe(1);
      expect(result.summary!.newDiscs).toBe(1);
    });

    it("should import JSON with metadata wrapper", () => {
      const dataWithMetadata = {
        metadata: {
          exportDate: new Date().toISOString(),
          totalDiscs: 1,
          version: "1.0",
        },
        discs: mockDiscs,
      };
      const jsonData = JSON.stringify(dataWithMetadata);
      const result = importFromJson(jsonData, []);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
      expect(result.data![0]).toEqual(mockDisc);
    });

    it("should handle invalid JSON", () => {
      const result = importFromJson("invalid json", []);

      expect(result.success).toBe(false);
      expect(result.error).toContain("Import failed");
    });

    it("should handle non-array data", () => {
      const jsonData = JSON.stringify({ not: "an array" });
      const result = importFromJson(jsonData, []);

      expect(result.success).toBe(false);
      expect(result.error).toContain("Data must be an array");
    });

    it("should validate disc data and report errors", () => {
      const invalidDisc = {
        id: "invalid-id", // Invalid UUID format
        manufacturer: "Innova",
        mold: "Destroyer",
        plasticType: "Champion",
        weight: 999, // Invalid weight (too high)
        condition: "invalid_condition", // Invalid condition
        flightNumbers: {
          speed: 99, // Invalid speed (too high)
          glide: 5,
          turn: -1,
          fade: 3,
        },
        color: "Blue",
        currentLocation: "invalid_location", // Invalid location
        createdAt: "2024-01-01T00:00:00.000Z",
        updatedAt: "2024-01-01T00:00:00.000Z",
      };
      const jsonData = JSON.stringify([invalidDisc]);
      const result = importFromJson(jsonData, []);

      expect(result.success).toBe(true); // Still succeeds but with validation errors
      expect(result.data).toHaveLength(0); // No valid discs
      expect(result.validationErrors).toBeDefined();
      expect(result.validationErrors!.length).toBeGreaterThan(0);
    });

    it("should detect duplicates", () => {
      const currentDiscs = [mockDisc];
      const jsonData = JSON.stringify([mockDisc]);
      const result = importFromJson(jsonData, currentDiscs);

      expect(result.success).toBe(true);
      expect(result.summary!.duplicates).toBe(1);
      expect(result.summary!.newDiscs).toBe(0);
    });
  });
});

describe("Error Handling", () => {
  it("should handle export errors gracefully", () => {
    const circularRef: any = {};
    circularRef.self = circularRef;
    const invalidDiscs = [circularRef] as any;

    const options: ExportOptions = {
      format: "json",
    };

    expect(() => exportCollection(invalidDiscs, options)).toThrow("Failed to export collection");
  });

  it("should handle import errors gracefully", () => {
    const result = importFromJson("", []);

    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });
});
