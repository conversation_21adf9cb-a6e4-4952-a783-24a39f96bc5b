/**
 * Search Components Usage Examples
 * 
 * This file demonstrates practical usage of the SearchInput and FilterBar components
 * with the useSearch hook and filter utilities.
 */

"use client";

import React from 'react';
import { SearchInput, FilterBar } from '@/components/search';
import { useSearch } from '@/hooks/useSearch';
import { useInventory } from '@/hooks/useInventory';
import { DiscCondition, Location } from '@/lib/types';
import { getFilterSuggestions } from '@/lib/filterUtils';

// ============================================================================
// BASIC SEARCH INPUT EXAMPLE
// ============================================================================

export function BasicSearchInputExample() {
  const { discs } = useInventory();
  const {
    searchState,
    filteredDiscs,
    setQuery,
    clearSearch,
    hasActiveSearch
  } = useSearch(discs);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Basic Search Input</h3>
      
      <SearchInput
        value={searchState.query}
        onChange={setQuery}
        onClear={clearSearch}
        placeholder="Search your disc collection..."
        isLoading={false}
        size="md"
        autoFocus
      />
      
      <div className="text-sm text-muted-foreground">
        {hasActiveSearch ? (
          <>Found {filteredDiscs.length} of {discs.length} discs</>
        ) : (
          <>Showing all {discs.length} discs</>
        )}
      </div>
    </div>
  );
}

// ============================================================================
// ADVANCED SEARCH WITH FILTERS EXAMPLE
// ============================================================================

export function AdvancedSearchWithFiltersExample() {
  const { discs } = useInventory();
  const {
    searchState,
    filteredDiscs,
    setQuery,
    setFilters,
    clearAll,
    hasActiveSearch,
    hasActiveFilters,
    filterSuggestions
  } = useSearch(discs, {
    debounceDelay: 300,
    syncWithURL: true,
    enableMetrics: true
  });

  const [isFilterCollapsed, setIsFilterCollapsed] = React.useState(false);

  // Get filter options from the current disc collection
  const filterOptions = React.useMemo(() => {
    const suggestions = getFilterSuggestions(discs);
    return {
      manufacturers: suggestions.manufacturers,
      conditions: Object.values(DiscCondition),
      locations: Object.values(Location),
      colors: suggestions.colors,
      plasticTypes: suggestions.plasticTypes,
    };
  }, [discs]);

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Advanced Search with Filters</h3>
      
      {/* Search Input */}
      <SearchInput
        value={searchState.query}
        onChange={setQuery}
        placeholder="Search discs by name, manufacturer, or notes..."
        isLoading={false}
        size="lg"
        showSearchIcon={true}
        showClearButton={true}
        debounceDelay={300}
      />
      
      {/* Filter Bar */}
      <FilterBar
        filters={searchState.filters}
        onChange={setFilters}
        options={filterOptions}
        isCollapsed={isFilterCollapsed}
        onToggleCollapse={setIsFilterCollapsed}
        showCollapseButton={true}
      />
      
      {/* Results Summary */}
      <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
        <div className="text-sm">
          <span className="font-medium">{filteredDiscs.length}</span> of{' '}
          <span className="font-medium">{discs.length}</span> discs
          {(hasActiveSearch || hasActiveFilters) && (
            <span className="text-muted-foreground ml-2">
              (filtered)
            </span>
          )}
        </div>
        
        {(hasActiveSearch || hasActiveFilters) && (
          <button
            onClick={clearAll}
            className="text-sm text-primary hover:text-primary/80 underline"
          >
            Clear all filters
          </button>
        )}
      </div>
      
      {/* Active Filters Display */}
      {(hasActiveSearch || hasActiveFilters) && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Active Filters:</h4>
          <div className="flex flex-wrap gap-2">
            {searchState.query && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary/10 text-primary">
                Search: "{searchState.query}"
              </span>
            )}
            {searchState.filters.manufacturers?.map((manufacturer) => (
              <span
                key={manufacturer}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
              >
                Manufacturer: {manufacturer}
              </span>
            ))}
            {searchState.filters.conditions?.map((condition) => (
              <span
                key={condition}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800"
              >
                Condition: {condition}
              </span>
            ))}
            {searchState.filters.speedRange && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
                Speed: {searchState.filters.speedRange.min}-{searchState.filters.speedRange.max}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// ============================================================================
// COMPACT SEARCH EXAMPLE
// ============================================================================

export function CompactSearchExample() {
  const { discs } = useInventory();
  const {
    searchState,
    filteredDiscs,
    setQuery,
    clearSearch
  } = useSearch(discs, {
    debounceDelay: 200,
    syncWithURL: false
  });

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold">Compact Search</h3>
      
      <SearchInput
        value={searchState.query}
        onChange={setQuery}
        onClear={clearSearch}
        placeholder="Quick search..."
        size="sm"
        showSearchIcon={true}
        showClearButton={true}
        className="max-w-md"
      />
      
      <p className="text-xs text-muted-foreground">
        {filteredDiscs.length} results
      </p>
    </div>
  );
}

// ============================================================================
// SEARCH WITH SUBMIT EXAMPLE
// ============================================================================

export function SearchWithSubmitExample() {
  const { discs } = useInventory();
  const [submittedQuery, setSubmittedQuery] = React.useState('');
  const {
    searchState,
    filteredDiscs,
    setQuery
  } = useSearch(discs, {
    debounceDelay: 500,
    syncWithURL: false
  });

  const handleSubmit = (query: string) => {
    setSubmittedQuery(query);
    console.log('Search submitted:', query);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Search with Submit</h3>
      
      <SearchInput
        value={searchState.query}
        onChange={setQuery}
        onSubmit={handleSubmit}
        placeholder="Type and press Enter to search..."
        size="md"
      />
      
      {submittedQuery && (
        <div className="p-3 bg-blue-50 rounded-md">
          <p className="text-sm text-blue-800">
            Last submitted search: <strong>"{submittedQuery}"</strong>
          </p>
        </div>
      )}
      
      <p className="text-sm text-muted-foreground">
        Found {filteredDiscs.length} discs
      </p>
    </div>
  );
}
