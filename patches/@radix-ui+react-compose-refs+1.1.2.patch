diff --git a/node_modules/@radix-ui/react-compose-refs/dist/index.js b/node_modules/@radix-ui/react-compose-refs/dist/index.js
index 1234567..abcdefg 100644
--- a/node_modules/@radix-ui/react-compose-refs/dist/index.js
+++ b/node_modules/@radix-ui/react-compose-refs/dist/index.js
@@ -38,9 +38,15 @@ module.exports = __toCommonJS(index_exports);
 // packages/react/compose-refs/src/compose-refs.tsx
 var React = __toESM(require("react"));
 function setRef(ref, value) {
+  // React 19 compatibility: prevent infinite loops
+  if (typeof ref === "object" && ref && "current" in ref && ref.current === value) {
+    return;
+  }
   if (typeof ref === "function") {
-    return ref(value);
+    try {
+      return ref(value);
+    } catch (error) {
+      console.warn("Ref composition error:", error);
+    }
   } else if (ref !== null && ref !== void 0) {
     ref.current = value;
   }
@@ -70,6 +76,9 @@ function composeRefs(...refs) {
 }
 function useComposedRefs(...refs) {
+  // React 19 compatibility: memoize refs to prevent infinite re-renders
+  const stableRefs = React.useMemo(() => refs, refs);
+  return React.useCallback(composeRefs(...stableRefs), stableRefs);
   return React.useCallback(composeRefs(...refs), refs);
 }
