/**
 * Test suite for FormField component
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { FormField, FormFieldGroup } from "@/components/forms/FormField";
import { setupTest, cleanupTest, testAccessibility, createMockFunction } from "../../utils/testUtils";

describe("FormField", () => {
  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders with basic props", () => {
      render(<FormField id="test-field" label="Test Label" />);
      
      const label = screen.getByText("Test Label");
      const input = screen.getByRole("textbox");
      
      expect(label).toBeInTheDocument();
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute("id", "test-field");
    });

    it("associates label with input correctly", () => {
      render(<FormField id="test-field" label="Test Label" />);
      
      const label = screen.getByText("Test Label");
      const input = screen.getByRole("textbox");
      
      expect(label).toHaveAttribute("for", "test-field");
      expect(input).toHaveAttribute("id", "test-field");
    });

    it("renders with placeholder", () => {
      render(<FormField id="test-field" label="Test Label" placeholder="Enter text..." />);
      
      const input = screen.getByPlaceholderText("Enter text...");
      expect(input).toBeInTheDocument();
    });

    it("renders with default value", () => {
      render(<FormField id="test-field" label="Test Label" value="Default value" />);
      
      const input = screen.getByDisplayValue("Default value");
      expect(input).toBeInTheDocument();
    });
  });

  describe("Input Types", () => {
    it("renders text input by default", () => {
      render(<FormField id="test-field" label="Test Label" />);
      
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "text");
    });

    it("renders email input", () => {
      render(<FormField id="test-field" label="Email" type="email" />);
      
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "email");
    });

    it("renders password input", () => {
      render(<FormField id="test-field" label="Password" type="password" />);
      
      const input = screen.getByLabelText("Password");
      expect(input).toHaveAttribute("type", "password");
    });

    it("renders number input", () => {
      render(<FormField id="test-field" label="Number" type="number" />);
      
      const input = screen.getByRole("spinbutton");
      expect(input).toHaveAttribute("type", "number");
    });
  });

  describe("Required Field", () => {
    it("shows required indicator when required", () => {
      render(<FormField id="test-field" label="Required Field" required />);
      
      const input = screen.getByRole("textbox");
      const requiredIndicator = screen.getByLabelText("Required field");
      
      expect(input).toBeRequired();
      expect(requiredIndicator).toBeInTheDocument();
    });

    it("does not show required indicator when not required", () => {
      render(<FormField id="test-field" label="Optional Field" />);
      
      const input = screen.getByRole("textbox");
      const requiredIndicator = screen.queryByLabelText("Required field");
      
      expect(input).not.toBeRequired();
      expect(requiredIndicator).not.toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("displays error message", () => {
      render(<FormField id="test-field" label="Test Label" error="This field is required" />);
      
      const errorMessage = screen.getByText("This field is required");
      expect(errorMessage).toBeInTheDocument();
    });

    it("applies error styling to input", () => {
      render(<FormField id="test-field" label="Test Label" error="This field is required" />);
      
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("aria-invalid", "true");
    });

    it("applies error styling to label", () => {
      render(<FormField id="test-field" label="Test Label" error="This field is required" />);
      
      const label = screen.getByText("Test Label");
      expect(label).toHaveClass("text-destructive");
    });

    it("associates error message with input via aria-describedby", () => {
      render(<FormField id="test-field" label="Test Label" error="This field is required" />);
      
      const input = screen.getByRole("textbox");
      const errorId = input.getAttribute("aria-describedby");
      
      expect(errorId).toContain("test-field-error");
      
      const errorElement = document.getElementById(errorId!.split(" ")[0]);
      expect(errorElement).toHaveTextContent("This field is required");
    });
  });

  describe("Help Text", () => {
    it("displays help text", () => {
      render(<FormField id="test-field" label="Test Label" helpText="This is helpful information" />);
      
      const helpText = screen.getByText("This is helpful information");
      expect(helpText).toBeInTheDocument();
    });

    it("associates help text with input via aria-describedby", () => {
      render(<FormField id="test-field" label="Test Label" helpText="This is helpful information" />);
      
      const input = screen.getByRole("textbox");
      const describedBy = input.getAttribute("aria-describedby");
      
      expect(describedBy).toContain("test-field-help");
    });

    it("combines error and help text in aria-describedby", () => {
      render(
        <FormField 
          id="test-field" 
          label="Test Label" 
          error="Error message"
          helpText="Help text"
        />
      );
      
      const input = screen.getByRole("textbox");
      const describedBy = input.getAttribute("aria-describedby");
      
      expect(describedBy).toContain("test-field-error");
      expect(describedBy).toContain("test-field-help");
    });
  });

  describe("Interactions", () => {
    it("handles value changes", () => {
      const handleChange = createMockFunction<(e: React.ChangeEvent<HTMLInputElement>) => void>();
      render(<FormField id="test-field" label="Test Label" onChange={handleChange} />);
      
      const input = screen.getByRole("textbox");
      fireEvent.change(input, { target: { value: "new value" } });
      
      expect(handleChange).toHaveBeenCalledTimes(1);
      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({
            value: "new value"
          })
        })
      );
    });

    it("handles blur events", () => {
      const handleBlur = createMockFunction<(e: React.FocusEvent<HTMLInputElement>) => void>();
      render(<FormField id="test-field" label="Test Label" onBlur={handleBlur} />);
      
      const input = screen.getByRole("textbox");
      fireEvent.focus(input);
      fireEvent.blur(input);
      
      expect(handleBlur).toHaveBeenCalledTimes(1);
    });

    it("is disabled when disabled prop is true", () => {
      render(<FormField id="test-field" label="Test Label" disabled />);
      
      const input = screen.getByRole("textbox");
      expect(input).toBeDisabled();
    });
  });

  describe("Custom Props", () => {
    it("applies custom className", () => {
      render(<FormField id="test-field" label="Test Label" className="custom-field" />);
      
      const container = screen.getByRole("textbox").closest(".custom-field");
      expect(container).toBeInTheDocument();
    });

    it("applies custom input className", () => {
      render(<FormField id="test-field" label="Test Label" inputClassName="custom-input" />);
      
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("custom-input");
    });

    it("applies custom label className", () => {
      render(<FormField id="test-field" label="Test Label" labelClassName="custom-label" />);
      
      const label = screen.getByText("Test Label");
      expect(label).toHaveClass("custom-label");
    });

    it("forwards additional input props", () => {
      render(
        <FormField 
          id="test-field" 
          label="Test Label" 
          inputProps={{
            maxLength: 10,
            autoComplete: "off",
            "data-testid": "custom-input"
          }}
        />
      );
      
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("maxlength", "10");
      expect(input).toHaveAttribute("autocomplete", "off");
      expect(input).toHaveAttribute("data-testid", "custom-input");
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(<FormField id="test-field" label="Accessible Field" />);
      await testAccessibility(container);
    });

    it("supports custom aria-describedby", () => {
      render(
        <FormField 
          id="test-field" 
          label="Test Label" 
          aria-describedby="custom-description"
        />
      );
      
      const input = screen.getByRole("textbox");
      const describedBy = input.getAttribute("aria-describedby");
      
      expect(describedBy).toContain("custom-description");
    });

    it("maintains focus management", () => {
      render(<FormField id="test-field" label="Test Label" />);
      
      const input = screen.getByRole("textbox");
      input.focus();
      expect(input).toHaveFocus();
    });
  });
});

describe("FormFieldGroup", () => {
  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders with title and children", () => {
      render(
        <FormFieldGroup title="Group Title">
          <FormField id="field1" label="Field 1" />
          <FormField id="field2" label="Field 2" />
        </FormFieldGroup>
      );
      
      const title = screen.getByText("Group Title");
      const field1 = screen.getByLabelText("Field 1");
      const field2 = screen.getByLabelText("Field 2");
      
      expect(title).toBeInTheDocument();
      expect(field1).toBeInTheDocument();
      expect(field2).toBeInTheDocument();
    });

    it("renders with description", () => {
      render(
        <FormFieldGroup title="Group Title" description="Group description">
          <FormField id="field1" label="Field 1" />
        </FormFieldGroup>
      );
      
      const description = screen.getByText("Group description");
      expect(description).toBeInTheDocument();
    });

    it("renders without title", () => {
      render(
        <FormFieldGroup>
          <FormField id="field1" label="Field 1" />
        </FormFieldGroup>
      );
      
      const field1 = screen.getByLabelText("Field 1");
      expect(field1).toBeInTheDocument();
    });

    it("uses fieldset and legend for semantic structure", () => {
      render(
        <FormFieldGroup title="Group Title">
          <FormField id="field1" label="Field 1" />
        </FormFieldGroup>
      );
      
      const fieldset = screen.getByRole("group");
      const legend = screen.getByText("Group Title");
      
      expect(fieldset).toBeInTheDocument();
      expect(legend.tagName).toBe("LEGEND");
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(
        <FormFieldGroup title="Accessible Group">
          <FormField id="field1" label="Field 1" />
        </FormFieldGroup>
      );
      await testAccessibility(container);
    });

    it("groups related fields semantically", () => {
      render(
        <FormFieldGroup title="Personal Information">
          <FormField id="firstName" label="First Name" />
          <FormField id="lastName" label="Last Name" />
        </FormFieldGroup>
      );
      
      const group = screen.getByRole("group", { name: "Personal Information" });
      expect(group).toBeInTheDocument();
    });
  });
});
