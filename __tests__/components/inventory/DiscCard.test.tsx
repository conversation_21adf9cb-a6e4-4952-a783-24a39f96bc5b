/**
 * Test suite for DiscCard component
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { DiscCard } from "@/components/inventory/DiscCard";
import { DiscCondition, Location } from "@/lib/types";
import { setupTest, cleanupTest, testAccessibility, createMockFunction, createMockDisc } from "../../utils/testUtils";

describe("DiscCard", () => {
  const mockDisc = createMockDisc({
    id: "test-disc-1",
    manufacturer: "Innova",
    mold: "Destroyer",
    plasticType: "Champion",
    weight: 175,
    condition: DiscCondition.NEW,
    color: "Blue",
    notes: "Test disc for unit testing",
    imageUrl: "https://example.com/disc.jpg",
  });

  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders disc information correctly", () => {
      render(<DiscCard disc={mockDisc} />);

      // Check for manufacturer and mold (displayed separately)
      expect(screen.getByText("Innova")).toBeInTheDocument();
      expect(screen.getByText("Destroyer")).toBeInTheDocument();

      // Check for plastic type and weight (they're in the same text node)
      expect(screen.getByText(/Champion.*175/)).toBeInTheDocument();

      // Check for condition badge
      expect(screen.getByText("New")).toBeInTheDocument();

      // Check for color
      expect(screen.getByText("Blue")).toBeInTheDocument();

      // Check for notes
      expect(screen.getByText("Test disc for unit testing")).toBeInTheDocument();
    });

    it("renders disc image when imageUrl is provided", () => {
      render(<DiscCard disc={mockDisc} />);

      const image = screen.getByAltText("Innova Destroyer (Champion) disc");
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute("src");
    });

    it("renders placeholder when no imageUrl is provided", () => {
      const discWithoutImage = createMockDisc({ imageUrl: undefined });
      render(<DiscCard disc={discWithoutImage} />);

      // Should show disc icon placeholder
      const placeholder = screen.getByRole("button").querySelector('[aria-hidden="true"]');
      expect(placeholder).toBeInTheDocument();
    });

    it("renders flight numbers display", () => {
      render(<DiscCard disc={mockDisc} />);

      // Flight numbers should be displayed
      expect(screen.getByText("12")).toBeInTheDocument(); // Speed
      expect(screen.getByText("5")).toBeInTheDocument(); // Glide
      expect(screen.getByText("-1")).toBeInTheDocument(); // Turn
      expect(screen.getByText("3")).toBeInTheDocument(); // Fade
    });

    it("renders color indicator", () => {
      render(<DiscCard disc={mockDisc} />);

      const colorIndicator = screen.getByLabelText("Disc color: Blue");
      expect(colorIndicator).toBeInTheDocument();
      // Check that the color indicator has the correct style attribute
      expect(colorIndicator).toHaveAttribute("style", expect.stringContaining("background-color: blue"));
    });
  });

  describe("Actions", () => {
    it("renders action buttons when showActions is true", () => {
      const onEdit = createMockFunction();
      const onDelete = createMockFunction();

      render(<DiscCard disc={mockDisc} onEdit={onEdit} onDelete={onDelete} showActions={true} />);

      const editButton = screen.getByLabelText(/edit innova destroyer/i);
      const deleteButton = screen.getByLabelText(/delete innova destroyer/i);

      expect(editButton).toBeInTheDocument();
      expect(deleteButton).toBeInTheDocument();
    });

    it("hides action buttons when showActions is false", () => {
      const onEdit = createMockFunction();
      const onDelete = createMockFunction();

      render(<DiscCard disc={mockDisc} onEdit={onEdit} onDelete={onDelete} showActions={false} />);

      const editButton = screen.queryByLabelText(/edit innova destroyer/i);
      const deleteButton = screen.queryByLabelText(/delete innova destroyer/i);

      expect(editButton).not.toBeInTheDocument();
      expect(deleteButton).not.toBeInTheDocument();
    });

    it("calls onEdit when edit button is clicked", async () => {
      const user = userEvent.setup();
      const onEdit = createMockFunction();

      render(<DiscCard disc={mockDisc} onEdit={onEdit} />);

      const editButton = screen.getByLabelText(/edit innova destroyer/i);
      await user.click(editButton);

      expect(onEdit).toHaveBeenCalledTimes(1);
      expect(onEdit).toHaveBeenCalledWith(mockDisc);
    });

    it("calls onDelete when delete button is clicked", async () => {
      const user = userEvent.setup();
      const onDelete = createMockFunction();

      render(<DiscCard disc={mockDisc} onDelete={onDelete} />);

      const deleteButton = screen.getByLabelText(/delete innova destroyer/i);
      await user.click(deleteButton);

      expect(onDelete).toHaveBeenCalledTimes(1);
      expect(onDelete).toHaveBeenCalledWith(mockDisc.id);
    });
  });

  describe("Selection", () => {
    it("shows selected state when isSelected is true", () => {
      render(<DiscCard disc={mockDisc} isSelected={true} />);

      const card = screen.getByRole("button");
      expect(card).toHaveAttribute("aria-pressed", "true");
    });

    it("shows unselected state when isSelected is false", () => {
      render(<DiscCard disc={mockDisc} isSelected={false} />);

      const card = screen.getByRole("button");
      expect(card).toHaveAttribute("aria-pressed", "false");
    });

    it("calls onSelect when card is clicked", async () => {
      const user = userEvent.setup();
      const onSelect = createMockFunction();

      render(<DiscCard disc={mockDisc} onSelect={onSelect} />);

      const card = screen.getByRole("button");
      await user.click(card);

      expect(onSelect).toHaveBeenCalledTimes(1);
      expect(onSelect).toHaveBeenCalledWith(mockDisc);
    });

    it("calls onSelect when Enter key is pressed", () => {
      const onSelect = createMockFunction();

      render(<DiscCard disc={mockDisc} onSelect={onSelect} />);

      const card = screen.getByRole("button");
      fireEvent.keyDown(card, { key: "Enter" });

      expect(onSelect).toHaveBeenCalledTimes(1);
      expect(onSelect).toHaveBeenCalledWith(mockDisc);
    });

    it("calls onSelect when Space key is pressed", () => {
      const onSelect = createMockFunction();

      render(<DiscCard disc={mockDisc} onSelect={onSelect} />);

      const card = screen.getByRole("button");
      fireEvent.keyDown(card, { key: " " });

      expect(onSelect).toHaveBeenCalledTimes(1);
      expect(onSelect).toHaveBeenCalledWith(mockDisc);
    });

    it("does not call onSelect for other keys", () => {
      const onSelect = createMockFunction();

      render(<DiscCard disc={mockDisc} onSelect={onSelect} />);

      const card = screen.getByRole("button");
      fireEvent.keyDown(card, { key: "Tab" });

      expect(onSelect).not.toHaveBeenCalled();
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(<DiscCard disc={mockDisc} />);
      await testAccessibility(container);
    });

    it("has proper ARIA attributes", () => {
      render(<DiscCard disc={mockDisc} isSelected={true} />);

      const card = screen.getByRole("button");
      expect(card).toHaveAttribute("aria-label", "Disc card for Innova Destroyer (Champion)");
      expect(card).toHaveAttribute("aria-pressed", "true");
      expect(card).toHaveAttribute("tabindex", "0");
    });

    it("has accessible action buttons", () => {
      const onEdit = createMockFunction();
      const onDelete = createMockFunction();

      render(<DiscCard disc={mockDisc} onEdit={onEdit} onDelete={onDelete} />);

      const editButton = screen.getByLabelText(/edit innova destroyer/i);
      const deleteButton = screen.getByLabelText(/delete innova destroyer/i);

      expect(editButton).toHaveAttribute("aria-label");
      expect(deleteButton).toHaveAttribute("aria-label");
    });

    it("supports keyboard navigation", () => {
      render(<DiscCard disc={mockDisc} />);

      const card = screen.getByRole("button");
      card.focus();
      expect(card).toHaveFocus();
    });
  });

  describe("Edge Cases", () => {
    it("handles disc without notes", () => {
      const discWithoutNotes = createMockDisc({ notes: undefined });
      render(<DiscCard disc={discWithoutNotes} />);

      // Should render without errors
      expect(screen.getByText("Innova")).toBeInTheDocument();
      expect(screen.getByText("Destroyer")).toBeInTheDocument();
    });

    it("handles very long disc names", () => {
      const discWithLongName = createMockDisc({
        manufacturer: "Very Long Manufacturer Name",
        mold: "Very Long Mold Name That Might Overflow",
      });

      render(<DiscCard disc={discWithLongName} />);

      expect(screen.getByText("Very Long Manufacturer Name")).toBeInTheDocument();
      expect(screen.getByText("Very Long Mold Name That Might Overflow")).toBeInTheDocument();
    });

    it("handles special characters in disc properties", () => {
      const discWithSpecialChars = createMockDisc({
        manufacturer: "Manu & Co.",
        mold: "Disc-X",
        color: "Blue/Green",
        notes: "Notes with special chars: !@#$%^&*()",
      });

      render(<DiscCard disc={discWithSpecialChars} />);

      expect(screen.getByText("Manu & Co.")).toBeInTheDocument();
      expect(screen.getByText("Disc-X")).toBeInTheDocument();
      expect(screen.getByText("Blue/Green")).toBeInTheDocument();
      expect(screen.getByText("Notes with special chars: !@#$%^&*()")).toBeInTheDocument();
    });

    it("forwards additional props to Card component", () => {
      render(<DiscCard disc={mockDisc} data-testid="custom-disc-card" className="custom-class" />);

      const card = screen.getByTestId("custom-disc-card");
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass("custom-class");
    });
  });

  describe("Integration", () => {
    it("works with different disc conditions", () => {
      const conditions = Object.values(DiscCondition);

      conditions.forEach((condition) => {
        const disc = createMockDisc({ condition });
        const { unmount } = render(<DiscCard disc={disc} />);

        // Should render without errors
        expect(screen.getByRole("button")).toBeInTheDocument();

        unmount();
      });
    });

    it("works with different locations", () => {
      const locations = Object.values(Location);

      locations.forEach((location) => {
        const disc = createMockDisc({ currentLocation: location });
        const { unmount } = render(<DiscCard disc={disc} />);

        // Should render without errors
        expect(screen.getByRole("button")).toBeInTheDocument();

        unmount();
      });
    });
  });
});
