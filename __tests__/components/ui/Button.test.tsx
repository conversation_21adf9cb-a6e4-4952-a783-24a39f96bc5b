/**
 * Test suite for Button component
 */

import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { Button } from "@/components/ui/button";
import { setupTest, cleanupTest, testAccessibility, createMockFunction } from "../../utils/testUtils";

describe("Button", () => {
  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders with default props", () => {
      render(<Button>Click me</Button>);
      
      const button = screen.getByRole("button", { name: "Click me" });
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute("data-slot", "button");
    });

    it("renders with custom className", () => {
      render(<Button className="custom-class">Button</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toHaveClass("custom-class");
    });

    it("renders as child component when asChild is true", () => {
      render(
        <Button asChild>
          <a href="/test">Link Button</a>
        </Button>
      );
      
      const link = screen.getByRole("link", { name: "Link Button" });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute("href", "/test");
      expect(link).toHaveAttribute("data-slot", "button");
    });
  });

  describe("Variants", () => {
    it("renders default variant", () => {
      render(<Button>Default</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders destructive variant", () => {
      render(<Button variant="destructive">Delete</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders outline variant", () => {
      render(<Button variant="outline">Outline</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders secondary variant", () => {
      render(<Button variant="secondary">Secondary</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders ghost variant", () => {
      render(<Button variant="ghost">Ghost</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders link variant", () => {
      render(<Button variant="link">Link</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });
  });

  describe("Sizes", () => {
    it("renders default size", () => {
      render(<Button>Default Size</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders small size", () => {
      render(<Button size="sm">Small</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders large size", () => {
      render(<Button size="lg">Large</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });

    it("renders icon size", () => {
      render(<Button size="icon">🔥</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
    });
  });

  describe("Interactions", () => {
    it("handles click events", () => {
      const handleClick = createMockFunction<() => void>();
      render(<Button onClick={handleClick}>Click me</Button>);
      
      const button = screen.getByRole("button");
      fireEvent.click(button);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it("handles keyboard events", () => {
      const handleClick = createMockFunction<() => void>();
      render(<Button onClick={handleClick}>Press me</Button>);
      
      const button = screen.getByRole("button");
      fireEvent.keyDown(button, { key: "Enter" });
      fireEvent.keyDown(button, { key: " " });
      
      // Note: React handles Enter and Space key presses as clicks automatically
      expect(button).toBeInTheDocument();
    });

    it("is disabled when disabled prop is true", () => {
      const handleClick = createMockFunction<() => void>();
      render(<Button disabled onClick={handleClick}>Disabled</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeDisabled();
      
      fireEvent.click(button);
      expect(handleClick).not.toHaveBeenCalled();
    });
  });

  describe("HTML Attributes", () => {
    it("forwards HTML attributes", () => {
      render(
        <Button 
          type="submit" 
          form="test-form"
          aria-label="Submit form"
          data-testid="submit-button"
        >
          Submit
        </Button>
      );
      
      const button = screen.getByRole("button");
      expect(button).toHaveAttribute("type", "submit");
      expect(button).toHaveAttribute("form", "test-form");
      expect(button).toHaveAttribute("aria-label", "Submit form");
      expect(button).toHaveAttribute("data-testid", "submit-button");
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(<Button>Accessible Button</Button>);
      await testAccessibility(container);
    });

    it("has proper ARIA attributes", () => {
      render(<Button aria-pressed="true">Toggle Button</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toHaveAttribute("aria-pressed", "true");
    });

    it("supports focus management", () => {
      render(<Button>Focusable Button</Button>);
      
      const button = screen.getByRole("button");
      button.focus();
      expect(button).toHaveFocus();
    });

    it("works with screen readers", () => {
      render(<Button aria-describedby="help-text">Action Button</Button>);
      
      const button = screen.getByRole("button");
      expect(button).toHaveAttribute("aria-describedby", "help-text");
    });
  });

  describe("Edge Cases", () => {
    it("handles empty children", () => {
      render(<Button></Button>);
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
      expect(button).toBeEmptyDOMElement();
    });

    it("handles complex children", () => {
      render(
        <Button>
          <span>Icon</span>
          <span>Text</span>
        </Button>
      );
      
      const button = screen.getByRole("button");
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent("IconText");
    });

    it("maintains ref forwarding", () => {
      let buttonRef: HTMLButtonElement | null = null;
      
      render(
        <Button ref={(ref) => { buttonRef = ref; }}>
          Ref Button
        </Button>
      );
      
      expect(buttonRef).toBeInstanceOf(HTMLButtonElement);
    });
  });
});
