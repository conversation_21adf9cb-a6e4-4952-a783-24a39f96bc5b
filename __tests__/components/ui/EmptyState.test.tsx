/**
 * Test suite for EmptyState component
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { EmptyState } from "@/components/ui/EmptyState";
import { setupTest, cleanupTest, testAccessibility, createMockFunction } from "../../utils/testUtils";

describe("EmptyState", () => {
  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders with default props", () => {
      render(<EmptyState />);
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toHaveTextContent("No items found");
      
      const description = screen.getByText("There are no items to display at the moment.");
      expect(description).toBeInTheDocument();
    });

    it("renders with custom title and description", () => {
      render(
        <EmptyState 
          title="No Discs Found" 
          description="Your disc collection is empty. Add some discs to get started!" 
        />
      );
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toHaveTextContent("No Discs Found");
      
      const description = screen.getByText("Your disc collection is empty. Add some discs to get started!");
      expect(description).toBeInTheDocument();
    });

    it("renders with custom icon", () => {
      const CustomIcon = () => <div data-testid="custom-icon">🎯</div>;
      render(<EmptyState icon={CustomIcon} />);
      
      const icon = screen.getByTestId("custom-icon");
      expect(icon).toBeInTheDocument();
    });

    it("renders with custom className", () => {
      render(<EmptyState className="custom-empty-state" />);
      
      const container = screen.getByRole("region");
      expect(container).toHaveClass("custom-empty-state");
    });
  });

  describe("Action Button", () => {
    it("renders action button when provided", () => {
      const handleAction = createMockFunction<() => void>();
      render(
        <EmptyState 
          actionLabel="Add Disc" 
          onAction={handleAction}
        />
      );
      
      const button = screen.getByRole("button", { name: "Add Disc" });
      expect(button).toBeInTheDocument();
    });

    it("calls onAction when button is clicked", () => {
      const handleAction = createMockFunction<() => void>();
      render(
        <EmptyState 
          actionLabel="Add Item" 
          onAction={handleAction}
        />
      );
      
      const button = screen.getByRole("button", { name: "Add Item" });
      fireEvent.click(button);
      
      expect(handleAction).toHaveBeenCalledTimes(1);
    });

    it("does not render action button when only actionLabel is provided", () => {
      render(<EmptyState actionLabel="Add Item" />);
      
      const button = screen.queryByRole("button", { name: "Add Item" });
      expect(button).not.toBeInTheDocument();
    });

    it("does not render action button when only onAction is provided", () => {
      const handleAction = createMockFunction<() => void>();
      render(<EmptyState onAction={handleAction} />);
      
      const button = screen.queryByRole("button");
      expect(button).not.toBeInTheDocument();
    });
  });

  describe("Variants", () => {
    it("renders default variant", () => {
      render(<EmptyState />);
      
      const container = screen.getByRole("region");
      expect(container).toBeInTheDocument();
    });

    it("renders search variant", () => {
      render(<EmptyState variant="search" />);
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toHaveTextContent("No results found");
      
      const description = screen.getByText("Try adjusting your search criteria or filters.");
      expect(description).toBeInTheDocument();
    });

    it("renders error variant", () => {
      render(<EmptyState variant="error" />);
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toHaveTextContent("Something went wrong");
      
      const description = screen.getByText("We encountered an error while loading your data.");
      expect(description).toBeInTheDocument();
    });

    it("renders loading variant", () => {
      render(<EmptyState variant="loading" />);
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toHaveTextContent("Loading...");
      
      const description = screen.getByText("Please wait while we load your data.");
      expect(description).toBeInTheDocument();
    });
  });

  describe("Size Variants", () => {
    it("renders default size", () => {
      render(<EmptyState />);
      
      const container = screen.getByRole("region");
      expect(container).toBeInTheDocument();
    });

    it("renders small size", () => {
      render(<EmptyState size="sm" />);
      
      const container = screen.getByRole("region");
      expect(container).toBeInTheDocument();
    });

    it("renders large size", () => {
      render(<EmptyState size="lg" />);
      
      const container = screen.getByRole("region");
      expect(container).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(<EmptyState />);
      await testAccessibility(container);
    });

    it("has proper semantic structure", () => {
      render(<EmptyState />);
      
      const region = screen.getByRole("region");
      expect(region).toBeInTheDocument();
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toBeInTheDocument();
    });

    it("supports custom ARIA labels", () => {
      render(<EmptyState aria-label="Empty disc collection" />);
      
      const region = screen.getByLabelText("Empty disc collection");
      expect(region).toBeInTheDocument();
    });

    it("has accessible action button", () => {
      const handleAction = createMockFunction<() => void>();
      render(
        <EmptyState 
          actionLabel="Add first disc" 
          onAction={handleAction}
        />
      );
      
      const button = screen.getByRole("button", { name: "Add first disc" });
      expect(button).toBeInTheDocument();
      expect(button).toBeEnabled();
    });
  });

  describe("Edge Cases", () => {
    it("handles empty title", () => {
      render(<EmptyState title="" />);
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toBeEmptyDOMElement();
    });

    it("handles empty description", () => {
      render(<EmptyState description="" />);
      
      const description = screen.queryByText("There are no items to display at the moment.");
      expect(description).not.toBeInTheDocument();
    });

    it("handles very long title", () => {
      const longTitle = "This is a very long title that might wrap to multiple lines and should still be displayed correctly";
      render(<EmptyState title={longTitle} />);
      
      const heading = screen.getByRole("heading", { level: 3 });
      expect(heading).toHaveTextContent(longTitle);
    });

    it("handles very long description", () => {
      const longDescription = "This is a very long description that contains multiple sentences and should wrap properly. It should maintain readability and proper spacing even when the content is extensive.";
      render(<EmptyState description={longDescription} />);
      
      const description = screen.getByText(longDescription);
      expect(description).toBeInTheDocument();
    });

    it("forwards additional props", () => {
      render(
        <EmptyState 
          data-testid="empty-state"
          role="status"
        />
      );
      
      const container = screen.getByTestId("empty-state");
      expect(container).toBeInTheDocument();
      expect(container).toHaveAttribute("role", "status");
    });
  });

  describe("Integration", () => {
    it("works with all variant combinations", () => {
      const variants = ["default", "search", "error", "loading"] as const;
      const sizes = ["sm", "md", "lg"] as const;
      
      variants.forEach((variant) => {
        sizes.forEach((size) => {
          const { unmount } = render(
            <EmptyState variant={variant} size={size} />
          );
          
          const container = screen.getByRole("region");
          expect(container).toBeInTheDocument();
          
          unmount();
        });
      });
    });

    it("maintains consistent structure across variants", () => {
      const variants = ["default", "search", "error", "loading"] as const;
      
      variants.forEach((variant) => {
        const { unmount } = render(<EmptyState variant={variant} />);
        
        const heading = screen.getByRole("heading", { level: 3 });
        expect(heading).toBeInTheDocument();
        
        unmount();
      });
    });
  });
});
