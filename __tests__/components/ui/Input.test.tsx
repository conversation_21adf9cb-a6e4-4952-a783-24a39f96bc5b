/**
 * Test suite for Input component
 */

import React from "react";
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { render, screen, fireEvent } from "@testing-library/react";
import { Input } from "@/components/ui/input";
import { setupTest, cleanupTest, testAccessibility, createMockFunction } from "../../utils/testUtils";

describe("Input", () => {
  beforeEach(setupTest);
  afterEach(cleanupTest);

  describe("Rendering", () => {
    it("renders with default props", () => {
      render(<Input />);

      const input = screen.getByRole("textbox");
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute("data-slot", "input");
    });

    it("renders with placeholder", () => {
      render(<Input placeholder="Enter text..." />);

      const input = screen.getByPlaceholderText("Enter text...");
      expect(input).toBeInTheDocument();
    });

    it("renders with custom className", () => {
      render(<Input className="custom-input" />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("custom-input");
    });

    it("renders with default value", () => {
      render(<Input defaultValue="Default text" />);

      const input = screen.getByDisplayValue("Default text");
      expect(input).toBeInTheDocument();
    });
  });

  describe("Input Types", () => {
    it("renders as text input by default", () => {
      render(<Input />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "text");
    });

    it("renders as email input", () => {
      render(<Input type="email" />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "email");
    });

    it("renders as password input", () => {
      render(<Input type="password" />);

      const input = screen.getByLabelText(/password/i) || screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "password");
    });

    it("renders as number input", () => {
      render(<Input type="number" />);

      const input = screen.getByRole("spinbutton");
      expect(input).toHaveAttribute("type", "number");
    });

    it("renders as search input", () => {
      render(<Input type="search" />);

      const input = screen.getByRole("searchbox");
      expect(input).toHaveAttribute("type", "search");
    });

    it("renders as file input", () => {
      render(<Input type="file" />);

      const input = screen.getByRole("textbox") || document.querySelector('input[type="file"]');
      expect(input).toHaveAttribute("type", "file");
    });
  });

  describe("Interactions", () => {
    it("handles value changes", () => {
      const handleChange = createMockFunction<(e: React.ChangeEvent<HTMLInputElement>) => void>();
      render(<Input onChange={handleChange} />);

      const input = screen.getByRole("textbox");
      fireEvent.change(input, { target: { value: "test value" } });

      expect(handleChange).toHaveBeenCalledTimes(1);
      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({
            value: "test value",
          }),
        })
      );
    });

    it("handles focus events", () => {
      const handleFocus = createMockFunction<() => void>();
      render(<Input onFocus={handleFocus} />);

      const input = screen.getByRole("textbox");
      fireEvent.focus(input);

      expect(handleFocus).toHaveBeenCalledTimes(1);
    });

    it("handles blur events", () => {
      const handleBlur = createMockFunction<() => void>();
      render(<Input onBlur={handleBlur} />);

      const input = screen.getByRole("textbox");
      fireEvent.focus(input);
      fireEvent.blur(input);

      expect(handleBlur).toHaveBeenCalledTimes(1);
    });

    it("handles keyboard events", () => {
      const handleKeyDown = createMockFunction<(e: React.KeyboardEvent) => void>();
      render(<Input onKeyDown={handleKeyDown} />);

      const input = screen.getByRole("textbox");
      fireEvent.keyDown(input, { key: "Enter" });

      expect(handleKeyDown).toHaveBeenCalledTimes(1);
    });
  });

  describe("States", () => {
    it("is disabled when disabled prop is true", () => {
      render(<Input disabled />);

      const input = screen.getByRole("textbox");
      expect(input).toBeDisabled();
    });

    it("is readonly when readOnly prop is true", () => {
      render(<Input readOnly />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("readonly");
    });

    it("is required when required prop is true", () => {
      render(<Input required />);

      const input = screen.getByRole("textbox");
      expect(input).toBeRequired();
    });

    it("shows invalid state with aria-invalid", () => {
      render(<Input aria-invalid="true" />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("aria-invalid", "true");
    });
  });

  describe("HTML Attributes", () => {
    it("forwards HTML attributes", () => {
      render(
        <Input
          name="test-input"
          id="test-id"
          autoComplete="email"
          maxLength={50}
          minLength={5}
          pattern="[a-z]+"
          data-testid="test-input"
        />
      );

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("name", "test-input");
      expect(input).toHaveAttribute("id", "test-id");
      expect(input).toHaveAttribute("autocomplete", "email");
      expect(input).toHaveAttribute("maxlength", "50");
      expect(input).toHaveAttribute("minlength", "5");
      expect(input).toHaveAttribute("pattern", "[a-z]+");
      expect(input).toHaveAttribute("data-testid", "test-input");
    });

    it("supports min and max for number inputs", () => {
      render(<Input type="number" min={0} max={100} step={5} />);

      const input = screen.getByRole("spinbutton");
      expect(input).toHaveAttribute("min", "0");
      expect(input).toHaveAttribute("max", "100");
      expect(input).toHaveAttribute("step", "5");
    });
  });

  describe("Accessibility", () => {
    it("has no accessibility violations", async () => {
      const { container } = render(<Input aria-label="Test input" />);
      await testAccessibility(container);
    });

    it("supports ARIA labels", () => {
      render(<Input aria-label="Search query" />);

      const input = screen.getByLabelText("Search query");
      expect(input).toBeInTheDocument();
    });

    it("supports ARIA descriptions", () => {
      render(<Input aria-describedby="help-text" />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("aria-describedby", "help-text");
    });

    it("supports form labels", () => {
      render(
        <div>
          <label htmlFor="test-input">Test Label</label>
          <Input id="test-input" />
        </div>
      );

      const input = screen.getByLabelText("Test Label");
      expect(input).toBeInTheDocument();
    });

    it("supports focus management", () => {
      render(<Input />);

      const input = screen.getByRole("textbox");
      input.focus();
      expect(input).toHaveFocus();
    });
  });

  describe("Edge Cases", () => {
    it("handles controlled input", () => {
      const TestComponent = () => {
        const [value, setValue] = React.useState("initial");
        return <Input value={value} onChange={(e) => setValue(e.target.value)} />;
      };

      render(<TestComponent />);

      const input = screen.getByDisplayValue("initial");
      expect(input).toBeInTheDocument();

      fireEvent.change(input, { target: { value: "updated" } });
      expect(screen.getByDisplayValue("updated")).toBeInTheDocument();
    });

    it("maintains ref forwarding", () => {
      let inputRef: HTMLInputElement | null = null;

      render(
        <Input
          ref={(ref) => {
            inputRef = ref;
          }}
        />
      );

      expect(inputRef).toBeInstanceOf(HTMLInputElement);
    });

    it("handles special characters in value", () => {
      render(<Input defaultValue="Special chars: !@#$%^&*()" />);

      const input = screen.getByDisplayValue("Special chars: !@#$%^&*()");
      expect(input).toBeInTheDocument();
    });
  });
});
