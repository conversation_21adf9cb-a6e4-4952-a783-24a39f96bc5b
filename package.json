{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest --run", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "postinstall": "patch-package"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.539.0", "next": "15.4.6", "react": "19.1.1", "react-dom": "19.1.1", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.2.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "@vitest/coverage-v8": "3.2.4", "eslint": "^9.33.0", "eslint-config-next": "15.4.6", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "patch-package": "^8.0.0", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vitest": "^3.2.4"}}