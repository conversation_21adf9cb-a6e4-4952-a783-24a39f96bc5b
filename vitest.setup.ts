import "@testing-library/jest-dom";
import { configureAxe, toHaveNoViolations } from "jest-axe";
import { vi } from "vitest";

// Configure jest-axe for accessibility testing
const axe = configureAxe({
  rules: {
    // Disable color-contrast rule for testing (can be flaky in jsdom)
    "color-contrast": { enabled: false },
  },
});

// Make axe available globally for tests
global.axe = axe;

// Extend expect with jest-axe matchers
expect.extend(toHaveNoViolations);

// Mock performance.now for testing
global.performance =
  global.performance ||
  ({
    now: () => Date.now(),
    mark: () => {},
    measure: () => {},
    getEntriesByName: () => [],
    getEntriesByType: () => [],
    clearMarks: () => {},
    clearMeasures: () => {},
  } as any);

// React 19 compatibility: Mock @radix-ui/react-compose-refs to prevent infinite loops
vi.mock("@radix-ui/react-compose-refs", () => ({
  composeRefs:
    (...refs: any[]) =>
    (node: any) => {
      refs.forEach((ref) => {
        if (typeof ref === "function") {
          try {
            ref(node);
          } catch (error) {
            console.warn("Ref composition error:", error);
          }
        } else if (ref != null && typeof ref === "object" && "current" in ref) {
          try {
            ref.current = node;
          } catch (error) {
            console.warn("Ref assignment error:", error);
          }
        }
      });
    },
  useComposedRefs: (...refs: any[]) => {
    return (node: any) => {
      refs.forEach((ref) => {
        if (typeof ref === "function") {
          try {
            ref(node);
          } catch (error) {
            console.warn("Composed ref error:", error);
          }
        } else if (ref != null && typeof ref === "object" && "current" in ref) {
          try {
            ref.current = node;
          } catch (error) {
            console.warn("Composed ref assignment error:", error);
          }
        }
      });
    };
  },
}));

// Suppress React 19 warnings in tests
const originalError = console.error;
console.error = (...args: any[]) => {
  if (
    typeof args[0] === "string" &&
    (args[0].includes("Maximum update depth exceeded") ||
      args[0].includes("Warning: An update to") ||
      args[0].includes("act(...)"))
  ) {
    return;
  }
  originalError.call(console, ...args);
};
