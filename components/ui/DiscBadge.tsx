/**
 * DiscBadge Component for Disc Golf Inventory Management System
 *
 * A specialized badge component for displaying disc conditions and categories
 * with condition-based color coding, accessible color contrast, proper ARIA labels,
 * and responsive sizing.
 */

import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { Badge } from "./badge";
import { cn } from "@/lib/utils";
import { DiscCondition } from "@/lib/types";
import { formatConditionText } from "@/lib/discUtils";

/**
 * Disc badge variants with condition-specific color coding
 * Colors chosen to meet WCAG 2.1 AA contrast requirements
 */
const discBadgeVariants = cva(
  "", // Base classes handled by Badge component
  {
    variants: {
      condition: {
        [DiscCondition.NEW]:
          "border-transparent bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300",
        [DiscCondition.GOOD]: "border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
        [DiscCondition.FAIR]: "border-transparent bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300",
        [DiscCondition.WORN]:
          "border-transparent bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
        [DiscCondition.DAMAGED]: "border-transparent bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      },
      size: {
        sm: "text-xs px-1.5 py-0.5",
        md: "text-xs px-2 py-0.5", // Default from base Badge
        lg: "text-sm px-2.5 py-1",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
);

/**
 * Props for the DiscBadge component
 */
export interface DiscBadgeProps
  extends Omit<React.ComponentProps<typeof Badge>, "variant">,
    VariantProps<typeof discBadgeVariants> {
  /** The disc condition to display (optional if text is provided) */
  condition?: DiscCondition;
  /** Optional custom text to display instead of condition text */
  text?: string;
  /** Optional custom children - if not provided, uses text or formatted condition text */
  children?: React.ReactNode;
}

/**
 * DiscBadge component for displaying disc conditions with appropriate styling
 *
 * @example
 * ```tsx
 * <DiscBadge condition={DiscCondition.NEW} size="md" />
 * <DiscBadge condition={DiscCondition.WELL_USED} size="sm">Custom Text</DiscBadge>
 * ```
 */
function DiscBadge({ condition, text, size, className, children, ...props }: DiscBadgeProps) {
  // Determine display text: children > text > formatted condition
  const displayText = children || text || (condition ? formatConditionText(condition) : "");

  // Determine aria-label based on what's being displayed
  const ariaLabel = text ? `Badge: ${text}` : condition ? `Disc condition: ${formatConditionText(condition)}` : "Badge";

  // Determine title for tooltip
  const titleText = condition ? formatConditionText(condition) : text || "";

  return (
    <Badge
      className={cn(discBadgeVariants({ condition, size }), className)}
      aria-label={ariaLabel}
      title={titleText}
      data-condition={condition?.toLowerCase()}
      {...props}
    >
      {displayText}
    </Badge>
  );
}

export { DiscBadge, discBadgeVariants };
