/**
 * StatsOverview Component for Disc Golf Inventory Management System
 *
 * A comprehensive dashboard component displaying collection statistics and insights
 * including total disc count, breakdown by manufacturer, condition distribution,
 * and flight number averages.
 */

"use client";

import * as React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DiscBadge } from "@/components/ui/DiscBadge";
import { cn } from "@/lib/utils";
import type { Disc, DiscCondition, Location } from "@/lib/types";
import {
  calculateCollectionValue,
  calculateAverageWeight,
  getMostCommonManufacturer,
  countDiscsByCondition,
  countDiscsByLocation,
  countDiscsByManufacturer,
  calculateAverageFlightNumbers,
} from "@/lib/discUtils";
import {
  BarChart3,
  Disc3,
  DollarSign,
  Weight,
  TrendingUp,
  Package,
  MapPin,
  Star,
} from "lucide-react";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface StatsOverviewProps {
  discs: Disc[];
  className?: string;
  showDetailedStats?: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    label: string;
  };
  className?: string;
}

// ============================================================================
// UTILITY COMPONENTS
// ============================================================================

/**
 * StatCard component for displaying individual statistics
 */
function StatCard({ title, value, description, icon: Icon, trend, className }: StatCardProps) {
  return (
    <Card className={cn("", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {trend && (
          <div className="flex items-center pt-1">
            <TrendingUp className="h-3 w-3 text-green-600 mr-1" />
            <span className="text-xs text-green-600">
              {trend.value > 0 ? "+" : ""}{trend.value} {trend.label}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * DistributionCard component for displaying data distributions
 */
function DistributionCard({
  title,
  data,
  icon: Icon,
  className,
}: {
  title: string;
  data: Array<{ label: string; count: number; percentage: number }>;
  icon: React.ComponentType<{ className?: string }>;
  className?: string;
}) {
  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-base">
          <Icon className="h-5 w-5" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {data.slice(0, 5).map((item, index) => (
            <div key={item.label} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{
                    backgroundColor: `hsl(${(index * 60) % 360}, 70%, 50%)`,
                  }}
                />
                <span className="text-sm font-medium">{item.label}</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold">{item.count}</div>
                <div className="text-xs text-muted-foreground">
                  {item.percentage.toFixed(1)}%
                </div>
              </div>
            </div>
          ))}
          {data.length > 5 && (
            <div className="text-xs text-muted-foreground pt-2 border-t">
              +{data.length - 5} more categories
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * StatsOverview component displaying comprehensive collection statistics
 *
 * @example
 * ```tsx
 * <StatsOverview
 *   discs={discs}
 *   showDetailedStats={true}
 * />
 * ```
 */
export function StatsOverview({ discs, className, showDetailedStats = true }: StatsOverviewProps) {
  // Calculate basic statistics
  const totalDiscs = discs.length;
  const totalValue = calculateCollectionValue(discs);
  const averageWeight = calculateAverageWeight(discs);
  const mostCommonManufacturer = getMostCommonManufacturer(discs);
  const averageFlightNumbers = calculateAverageFlightNumbers(discs);

  // Calculate distributions
  const conditionCounts = countDiscsByCondition(discs);
  const locationCounts = countDiscsByLocation(discs);
  const manufacturerCounts = countDiscsByManufacturer(discs);

  // Prepare distribution data
  const conditionData = Object.entries(conditionCounts)
    .map(([condition, count]) => ({
      label: condition.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()),
      count,
      percentage: totalDiscs > 0 ? (count / totalDiscs) * 100 : 0,
    }))
    .filter(item => item.count > 0)
    .sort((a, b) => b.count - a.count);

  const locationData = Object.entries(locationCounts)
    .map(([location, count]) => ({
      label: location.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase()),
      count,
      percentage: totalDiscs > 0 ? (count / totalDiscs) * 100 : 0,
    }))
    .filter(item => item.count > 0)
    .sort((a, b) => b.count - a.count);

  const manufacturerData = Object.entries(manufacturerCounts)
    .map(([manufacturer, count]) => ({
      label: manufacturer,
      count,
      percentage: totalDiscs > 0 ? (count / totalDiscs) * 100 : 0,
    }))
    .filter(item => item.count > 0)
    .sort((a, b) => b.count - a.count);

  if (totalDiscs === 0) {
    return (
      <div className={cn("space-y-6", className)}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Collection Statistics
            </CardTitle>
            <CardDescription>
              Your collection insights and analytics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <Disc3 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-600">No discs in collection</p>
              <p className="text-sm text-gray-500">Add some discs to see statistics</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
          <BarChart3 className="h-6 w-6" />
          Collection Statistics
        </h2>
        <p className="text-muted-foreground">
          Insights and analytics for your disc golf collection
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Discs"
          value={totalDiscs}
          description="Discs in collection"
          icon={Disc3}
        />
        <StatCard
          title="Collection Value"
          value={totalValue > 0 ? `$${totalValue.toFixed(2)}` : "N/A"}
          description="Total purchase value"
          icon={DollarSign}
        />
        <StatCard
          title="Average Weight"
          value={averageWeight > 0 ? `${averageWeight}g` : "N/A"}
          description="Average disc weight"
          icon={Weight}
        />
        <StatCard
          title="Top Manufacturer"
          value={mostCommonManufacturer || "N/A"}
          description="Most common brand"
          icon={Star}
        />
      </div>

      {/* Flight Numbers Summary */}
      {averageFlightNumbers && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Average Flight Numbers
            </CardTitle>
            <CardDescription>
              Average flight characteristics across your collection
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {averageFlightNumbers.speed.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Speed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {averageFlightNumbers.glide.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Glide</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {averageFlightNumbers.turn.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Turn</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {averageFlightNumbers.fade.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">Fade</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Statistics */}
      {showDetailedStats && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <DistributionCard
            title="By Manufacturer"
            data={manufacturerData}
            icon={Package}
          />
          <DistributionCard
            title="By Condition"
            data={conditionData}
            icon={DiscBadge}
          />
          <DistributionCard
            title="By Location"
            data={locationData}
            icon={MapPin}
          />
        </div>
      )}
    </div>
  );
}
