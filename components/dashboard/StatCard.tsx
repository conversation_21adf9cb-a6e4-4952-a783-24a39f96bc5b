/**
 * StatCard Component for Disc Golf Inventory Management System
 *
 * A reusable card component for displaying statistics with icons, trends,
 * and responsive design for dashboard layouts.
 */

"use client";

import * as React from "react";
import { Card, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: {
    value: number;
    label: string;
    direction?: "up" | "down" | "neutral";
  };
  variant?: "default" | "compact" | "detailed";
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * StatCard component for displaying individual statistics
 *
 * @example
 * ```tsx
 * <StatCard
 *   title="Total Discs"
 *   value={42}
 *   description="Discs in collection"
 *   icon={Disc3}
 *   trend={{ value: 5, label: "this month", direction: "up" }}
 * />
 * ```
 */
export function StatCard({
  title,
  value,
  description,
  icon: Icon,
  trend,
  variant = "default",
  className,
}: StatCardProps) {
  const getTrendColor = (direction?: "up" | "down" | "neutral") => {
    switch (direction) {
      case "up":
        return "text-green-600";
      case "down":
        return "text-red-600";
      case "neutral":
      default:
        return "text-blue-600";
    }
  };

  const getTrendIcon = (direction?: "up" | "down" | "neutral") => {
    switch (direction) {
      case "up":
        return TrendingUp;
      case "down":
        return TrendingDown;
      case "neutral":
      default:
        return TrendingUp;
    }
  };

  if (variant === "compact") {
    return (
      <Card className={cn("", className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
            </div>
            <Icon className="h-8 w-8 text-muted-foreground" />
          </div>
          {description && (
            <p className="text-xs text-muted-foreground mt-2">{description}</p>
          )}
        </CardContent>
      </Card>
    );
  }

  if (variant === "detailed") {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{value}</div>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
          {trend && (
            <div className="flex items-center pt-2">
              {React.createElement(getTrendIcon(trend.direction), {
                className: cn("h-3 w-3 mr-1", getTrendColor(trend.direction)),
              })}
              <span className={cn("text-xs", getTrendColor(trend.direction))}>
                {trend.value > 0 ? "+" : ""}{trend.value} {trend.label}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className={cn("", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {trend && (
          <div className="flex items-center pt-1">
            {React.createElement(getTrendIcon(trend.direction), {
              className: cn("h-3 w-3 mr-1", getTrendColor(trend.direction)),
            })}
            <span className={cn("text-xs", getTrendColor(trend.direction))}>
              {trend.value > 0 ? "+" : ""}{trend.value} {trend.label}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// ============================================================================
// UTILITY COMPONENTS
// ============================================================================

/**
 * StatCardGrid component for organizing multiple StatCards
 */
export interface StatCardGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export function StatCardGrid({ children, columns = 4, className }: StatCardGridProps) {
  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
  };

  return (
    <div className={cn("grid gap-4", gridClasses[columns], className)}>
      {children}
    </div>
  );
}

/**
 * StatCardSection component for grouping related statistics
 */
export interface StatCardSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function StatCardSection({ title, description, children, className }: StatCardSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div>
        <h3 className="text-lg font-semibold">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      {children}
    </div>
  );
}
