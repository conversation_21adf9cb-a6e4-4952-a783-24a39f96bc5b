/**
 * ImportDialog Component for Disc Golf Inventory Management System
 *
 * A dialog component for importing disc collection data with file upload,
 * validation feedback, and import preview.
 */

"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Upload, FileText, AlertCircle, CheckCircle, Info, X } from "lucide-react";
import { cn } from "@/lib/utils";
import type { Disc } from "@/lib/types";
import { importFromFile, type ImportResult } from "@/lib/exportImport";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface ImportDialogProps {
  currentDiscs: Disc[];
  onImport: (discs: Disc[]) => Promise<void>;
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

interface ImportState {
  selectedFile: File | null;
  isImporting: boolean;
  importResult: ImportResult | null;
  error: string | null;
  dragActive: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

/**
 * ImportDialog component for importing disc collection data
 *
 * @example
 * ```tsx
 * <ImportDialog
 *   currentDiscs={discs}
 *   onImport={handleImport}
 *   isOpen={showImportDialog}
 *   onClose={() => setShowImportDialog(false)}
 * />
 * ```
 */
export function ImportDialog({ currentDiscs, onImport, isOpen = true, onClose, className }: ImportDialogProps) {
  const [state, setState] = React.useState<ImportState>({
    selectedFile: null,
    isImporting: false,
    importResult: null,
    error: null,
    dragActive: false,
  });

  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const resetState = () => {
    setState({
      selectedFile: null,
      isImporting: false,
      importResult: null,
      error: null,
      dragActive: false,
    });
  };

  const handleFileSelect = (file: File) => {
    setState(prev => ({
      ...prev,
      selectedFile: file,
      importResult: null,
      error: null,
    }));
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setState(prev => ({ ...prev, dragActive: true }));
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setState(prev => ({ ...prev, dragActive: false }));
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setState(prev => ({ ...prev, dragActive: false }));

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handlePreview = async () => {
    if (!state.selectedFile) return;

    setState(prev => ({ ...prev, isImporting: true, error: null }));

    try {
      const result = await importFromFile(state.selectedFile, currentDiscs);
      setState(prev => ({ ...prev, importResult: result }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Preview failed",
      }));
    } finally {
      setState(prev => ({ ...prev, isImporting: false }));
    }
  };

  const handleConfirmImport = async () => {
    if (!state.importResult?.success || !state.importResult.data) return;

    setState(prev => ({ ...prev, isImporting: true }));

    try {
      await onImport(state.importResult.data);
      onClose?.();
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : "Import failed",
      }));
    } finally {
      setState(prev => ({ ...prev, isImporting: false }));
    }
  };

  const handleClose = () => {
    resetState();
    onClose?.();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <Card className={cn("w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto", className)}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Import Collection
              </CardTitle>
              <CardDescription>
                Import disc collection data from a JSON file
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={handleClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* File Upload Area */}
          {!state.importResult && (
            <div className="space-y-4">
              <div
                className={cn(
                  "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                  state.dragActive
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300 hover:border-gray-400"
                )}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <FileText className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    {state.selectedFile ? state.selectedFile.name : "Drop your JSON file here"}
                  </p>
                  <p className="text-sm text-gray-500">
                    or{" "}
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-700 underline"
                    >
                      browse to upload
                    </button>
                  </p>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>

              {state.selectedFile && (
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium">{state.selectedFile.name}</span>
                    <span className="text-xs text-gray-500">
                      ({(state.selectedFile.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setState(prev => ({ ...prev, selectedFile: null }))}
                  >
                    Remove
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Import Preview */}
          {state.importResult && (
            <div className="space-y-4">
              {state.importResult.success ? (
                <div className="space-y-4">
                  <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-700">File validated successfully!</span>
                  </div>

                  {/* Import Summary */}
                  {state.importResult.summary && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="p-3 bg-blue-50 rounded-md">
                        <div className="text-2xl font-bold text-blue-700">
                          {state.importResult.summary.totalImported}
                        </div>
                        <div className="text-sm text-blue-600">Valid Discs</div>
                      </div>
                      <div className="p-3 bg-orange-50 rounded-md">
                        <div className="text-2xl font-bold text-orange-700">
                          {state.importResult.summary.newDiscs}
                        </div>
                        <div className="text-sm text-orange-600">New Discs</div>
                      </div>
                      <div className="p-3 bg-yellow-50 rounded-md">
                        <div className="text-2xl font-bold text-yellow-700">
                          {state.importResult.summary.duplicates}
                        </div>
                        <div className="text-sm text-yellow-600">Duplicates</div>
                      </div>
                      <div className="p-3 bg-red-50 rounded-md">
                        <div className="text-2xl font-bold text-red-700">
                          {state.importResult.summary.totalErrors}
                        </div>
                        <div className="text-sm text-red-600">Errors</div>
                      </div>
                    </div>
                  )}

                  {/* Validation Errors */}
                  {state.importResult.validationErrors && state.importResult.validationErrors.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-red-700">Validation Errors:</Label>
                      <div className="max-h-32 overflow-y-auto space-y-1">
                        {state.importResult.validationErrors.slice(0, 5).map((error, index) => (
                          <div key={index} className="text-xs text-red-600 p-2 bg-red-50 rounded">
                            Row {error.index + 1}: {error.message}
                            {error.field && ` (${error.field})`}
                          </div>
                        ))}
                        {state.importResult.validationErrors.length > 5 && (
                          <div className="text-xs text-gray-500 p-2">
                            ... and {state.importResult.validationErrors.length - 5} more errors
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-700">{state.importResult.error}</span>
                </div>
              )}
            </div>
          )}

          {/* Error Message */}
          {state.error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm text-red-700">{state.error}</span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={state.isImporting}
              className="flex-1"
            >
              Cancel
            </Button>

            {!state.importResult ? (
              <Button
                onClick={handlePreview}
                disabled={!state.selectedFile || state.isImporting}
                className="flex-1"
              >
                {state.isImporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Validating...
                  </>
                ) : (
                  <>
                    <Info className="h-4 w-4 mr-2" />
                    Preview Import
                  </>
                )}
              </Button>
            ) : (
              <Button
                onClick={handleConfirmImport}
                disabled={!state.importResult.success || state.isImporting}
                className="flex-1"
              >
                {state.isImporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Importing...
                  </>
                ) : (
                  <>
                    <Upload className="h-4 w-4 mr-2" />
                    Confirm Import
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
