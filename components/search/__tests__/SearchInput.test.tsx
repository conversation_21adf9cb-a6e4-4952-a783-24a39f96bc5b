/**
 * Test suite for SearchInput component
 */

import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { SearchInput } from "../SearchInput";

describe("SearchInput", () => {
  it("renders with basic props", () => {
    const mockOnChange = vi.fn();

    render(<SearchInput value="" onChange={mockOnChange} placeholder="Search discs..." />);

    const input = screen.getByRole("searchbox");
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute("placeholder", "Search discs...");
  });

  it("calls onChange when typing", async () => {
    const mockOnChange = vi.fn();

    render(<SearchInput value="" onChange={mockOnChange} debounceDelay={100} />);

    const input = screen.getByRole("searchbox");
    fireEvent.change(input, { target: { value: "test" } });

    // Wait for debounce
    await waitFor(
      () => {
        expect(mockOnChange).toHaveBeenCalledWith("test");
      },
      { timeout: 200 }
    );
  });

  it("shows clear button when value is present", () => {
    const mockOnChange = vi.fn();
    const mockOnClear = vi.fn();

    render(<SearchInput value="test query" onChange={mockOnChange} onClear={mockOnClear} />);

    const clearButton = screen.getByRole("button", { name: /clear search/i });
    expect(clearButton).toBeInTheDocument();
  });

  it("clears input when clear button is clicked", () => {
    const mockOnChange = vi.fn();
    const mockOnClear = vi.fn();

    render(<SearchInput value="test query" onChange={mockOnChange} onClear={mockOnClear} />);

    const clearButton = screen.getByRole("button", { name: /clear search/i });
    fireEvent.click(clearButton);

    expect(mockOnChange).toHaveBeenCalledWith("");
    expect(mockOnClear).toHaveBeenCalled();
  });

  it("clears input when Escape key is pressed", () => {
    const mockOnChange = vi.fn();
    const mockOnClear = vi.fn();

    render(<SearchInput value="test query" onChange={mockOnChange} onClear={mockOnClear} />);

    const input = screen.getByRole("searchbox");
    fireEvent.keyDown(input, { key: "Escape" });

    expect(mockOnChange).toHaveBeenCalledWith("");
    expect(mockOnClear).toHaveBeenCalled();
  });

  it("shows loading state", () => {
    const mockOnChange = vi.fn();

    render(<SearchInput value="" onChange={mockOnChange} isLoading={true} />);

    expect(screen.getByText("Searching...")).toBeInTheDocument();
  });

  it("calls onSubmit when Enter key is pressed", () => {
    const mockOnChange = vi.fn();
    const mockOnSubmit = vi.fn();

    render(<SearchInput value="test query" onChange={mockOnChange} onSubmit={mockOnSubmit} />);

    const input = screen.getByRole("searchbox");
    fireEvent.keyDown(input, { key: "Enter" });

    expect(mockOnSubmit).toHaveBeenCalledWith("test query");
  });
});
