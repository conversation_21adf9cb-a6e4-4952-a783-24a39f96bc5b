/**
 * SearchInput Component for Disc Golf Inventory Management System
 *
 * A search input component with debounced input handling, clear button,
 * loading state, and keyboard shortcuts for enhanced user experience.
 */

"use client";

import * as React from "react";
import { Search, X, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

/**
 * Props for the SearchInput component
 */
export interface SearchInputProps {
  /** Current search query value */
  value: string;
  /** Callback when search value changes */
  onChange: (value: string) => void;
  /** Callback when search is cleared */
  onClear?: () => void;
  /** Placeholder text for the input */
  placeholder?: string;
  /** Whether the search is currently loading */
  isLoading?: boolean;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Size variant */
  size?: "sm" | "md" | "lg";
  /** Whether to show the search icon */
  showSearchIcon?: boolean;
  /** Whether to show the clear button */
  showClearButton?: boolean;
  /** Debounce delay in milliseconds */
  debounceDelay?: number;
  /** Auto-focus the input on mount */
  autoFocus?: boolean;
  /** Callback when Enter key is pressed */
  onSubmit?: (value: string) => void;
}

// ============================================================================
// COMPONENT VARIANTS
// ============================================================================

const searchInputVariants = {
  size: {
    sm: {
      input: "h-8 text-sm pl-8 pr-8",
      icon: "h-3.5 w-3.5 left-2.5",
      clearButton: "h-6 w-6 right-1",
      clearIcon: "h-3 w-3",
    },
    md: {
      input: "h-9 text-sm pl-9 pr-9",
      icon: "h-4 w-4 left-2.5",
      clearButton: "h-7 w-7 right-1",
      clearIcon: "h-3.5 w-3.5",
    },
    lg: {
      input: "h-10 text-base pl-10 pr-10",
      icon: "h-5 w-5 left-2.5",
      clearButton: "h-8 w-8 right-1",
      clearIcon: "h-4 w-4",
    },
  },
};

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Custom hook for debounced search input
 */
function useDebounceCallback(callback: (value: string) => void, delay: number) {
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  const debouncedCallback = React.useCallback(
    (value: string) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => callback(value), delay);
    },
    [callback, delay]
  );

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

/**
 * SearchInput component for search functionality
 *
 * @example
 * ```tsx
 * // Basic usage
 * <SearchInput
 *   value={searchQuery}
 *   onChange={setSearchQuery}
 *   placeholder="Search discs..."
 * />
 *
 * // With loading state and custom size
 * <SearchInput
 *   value={searchQuery}
 *   onChange={setSearchQuery}
 *   isLoading={isSearching}
 *   size="lg"
 *   onClear={() => setSearchQuery("")}
 * />
 *
 * // With debounced search and submit
 * <SearchInput
 *   value={searchQuery}
 *   onChange={setSearchQuery}
 *   debounceDelay={500}
 *   onSubmit={handleSearch}
 *   autoFocus
 * />
 * ```
 */
export function SearchInput({
  value,
  onChange,
  onClear,
  placeholder = "Search...",
  isLoading = false,
  disabled = false,
  className,
  size = "md",
  showSearchIcon = true,
  showClearButton = true,
  debounceDelay = 300,
  autoFocus = false,
  onSubmit,
}: SearchInputProps) {
  const [internalValue, setInternalValue] = React.useState(value);
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Debounced onChange callback
  const debouncedOnChange = useDebounceCallback(onChange, debounceDelay);

  // Sync internal value with external value
  React.useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // Auto-focus handling
  React.useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Get size-specific classes
  const sizeClasses = searchInputVariants.size[size];

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    debouncedOnChange(newValue);
  };

  // Handle clear button click
  const handleClear = () => {
    setInternalValue("");
    onChange("");
    onClear?.();
    inputRef.current?.focus();
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Escape") {
      e.preventDefault();
      handleClear();
    } else if (e.key === "Enter" && onSubmit) {
      e.preventDefault();
      onSubmit(internalValue);
    }
  };

  // Determine if clear button should be shown
  const shouldShowClearButton = showClearButton && internalValue.length > 0 && !disabled;

  return (
    <div className={cn("relative", className)}>
      {/* Search Icon */}
      {showSearchIcon && (
        <div
          className={cn(
            "absolute top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none",
            sizeClasses.icon
          )}
        >
          {isLoading ? <Loader2 className="animate-spin" /> : <Search />}
        </div>
      )}

      {/* Input Field */}
      <Input
        ref={inputRef}
        type="search"
        value={internalValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        className={cn(sizeClasses.input, showSearchIcon && "pl-9", shouldShowClearButton && "pr-9")}
        aria-label="Search input"
        aria-describedby={isLoading ? "search-loading" : undefined}
      />

      {/* Clear Button */}
      {shouldShowClearButton && (
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={handleClear}
          className={cn(
            "absolute top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground",
            sizeClasses.clearButton
          )}
          aria-label="Clear search"
          tabIndex={-1}
        >
          <X className={sizeClasses.clearIcon} />
        </Button>
      )}

      {/* Screen reader loading indicator */}
      {isLoading && (
        <span id="search-loading" className="sr-only">
          Searching...
        </span>
      )}
    </div>
  );
}

// ============================================================================
// EXPORTS
// ============================================================================

export default SearchInput;
